import type {CreateQueryOptions, CreateQueryResult} from '@tanstack/svelte-query';

/**
 * Base interface for all entity stores in the application.
 * Provides common patterns for data fetching, filtering, sorting, and state management.
 */
export interface BaseEntity {
    id: string;
    created_at: string;
    updated_at: string;
}

/**
 * Common filter interface that can be extended by specific stores
 */
export interface BaseFilters {
    searchQuery: string;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
}

/**
 * Base store actions interface
 */
export interface BaseStoreActions<TFilters extends BaseFilters> {
    setSearchQuery(query: string): void;

    setSorting(sortBy: string, sortOrder: 'asc' | 'desc'): void;

    clearFilters(): void;

    reset(): void;
}

/**
 * Utility function to create standardized query options
 */
export function createBaseQueryOptions<TEntity extends BaseEntity>(
    queryKey: string[],
    fetchFn: () => Promise<TEntity[]>,
    options?: {
        gcTime?: number;
        staleTime?: number;
        refetchOnWindowFocus?: boolean;
        refetchOnMount?: boolean;
    },
): CreateQueryOptions<TEntity[], Error> {
    return {
        queryKey,
        queryFn: fetchFn,
        staleTime: options?.staleTime ?? 5 * 60 * 1000, // Consider data fresh for 5 minutes by default
        gcTime: options?.gcTime ?? 30 * 24 * 60 * 60 * 1000, // 1 month cache retention
        refetchOnWindowFocus: options?.refetchOnWindowFocus ?? false,
        refetchOnMount: options?.refetchOnMount ?? false, // Don't refetch on mount if we have cached data
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors (client errors)
            if (error && 'status' in error && typeof error.status === 'number') {
                if (error.status >= 400 && error.status < 500) {
                    return false;
                }
            }
            // Retry up to 3 times for other errors
            return failureCount < 3;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    };
}

/**
 * Utility function for common search filtering
 */
export function createSearchFilter<TEntity extends BaseEntity>(
    searchFields: (keyof TEntity)[],
): (entities: TEntity[], searchQuery: string) => TEntity[] {
    return (entities: TEntity[], searchQuery: string) => {
        if (!searchQuery.trim()) {
            return entities;
        }

        const query = searchQuery.toLowerCase().trim();
        return entities.filter(entity =>
            searchFields.some(field => {
                const value = entity[field];
                if (typeof value === 'string') {
                    return value.toLowerCase().includes(query);
                }
                return false;
            }),
        );
    };
}

/**
 * Utility function for common sorting
 */
export function createSortFunction<TEntity extends BaseEntity>(): (
    entities: TEntity[],
    sortBy: string,
    sortOrder: 'asc' | 'desc',
) => TEntity[] {
    return (entities: TEntity[], sortBy: string, sortOrder: 'asc' | 'desc') => {
        return [...entities].sort((a, b) => {
            let aValue: string | Date | number;
            let bValue: string | Date | number;

            // Handle nested properties (e.g., 'user.name')
            const getValue = (obj: any, path: string) => {
                return path.split('.').reduce((current, key) => current?.[key], obj);
            };

            aValue = getValue(a, sortBy);
            bValue = getValue(b, sortBy);

            // Handle different data types
            if (sortBy.includes('created_at') || sortBy.includes('updated_at')) {
                aValue = new Date(aValue as string);
                bValue = new Date(bValue as string);
            } else if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            // Perform comparison
            if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
            return 0;
        });
    };
}

/**
 * Memoization utility for expensive operations
 */
interface MemoCache<TArgs extends any[], TResult> {
    key: string;
    result: TResult;
}

export function createMemoizedFunction<TArgs extends any[], TResult>(
    fn: (...args: TArgs) => TResult,
    keyGenerator: (...args: TArgs) => string,
    maxCacheSize: number = 10,
): (...args: TArgs) => TResult {
    const cache = new Map<string, MemoCache<TArgs, TResult>>();

    return (...args: TArgs): TResult => {
        const key = keyGenerator(...args);

        // Check if result is cached
        if (cache.has(key)) {
            return cache.get(key)!.result;
        }

        // Calculate new result
        const result = fn(...args);

        // Store in cache with size limit
        if (cache.size >= maxCacheSize) {
            // Remove oldest entry (first in Map)
            const firstKey = cache.keys().next().value;
            if (firstKey !== undefined) {
                cache.delete(firstKey);
            }
        }

        cache.set(key, {key, result});
        return result;
    };
}

/**
 * Create a key generator for filtering and sorting operations
 */
export function createFilterSortKey<TEntity extends BaseEntity, TFilters extends BaseFilters>(
    entities: TEntity[],
    filters: TFilters,
): string {
    // Create a hash-like key based on entities length, filters, and a sample of entity IDs
    const entityKey = entities.length > 0
        ? `${entities.length}-${entities[0]?.id}-${entities[entities.length - 1]?.id}`
        : '0';

    const filtersKey = JSON.stringify({
        searchQuery: filters.searchQuery,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
    });

    return `${entityKey}:${filtersKey}`;
}

/**
 * Error handling utility for stores
 */
export interface StoreError {
    message: string;
    code?: string;
    details?: string;
}

export function handleStoreError(error: unknown): StoreError {
    if (error && typeof error === 'object' && 'message' in error) {
        return {
            message: (error as any).message || 'An unexpected error occurred',
            code: (error as any).code,
            details: (error as any).details,
        };
    }

    if (typeof error === 'string') {
        return {message: error};
    }

    return {message: 'An unexpected error occurred'};
}
